import { PrismaClient } from '@prisma/client';
import { env } from '../config/env';

const getPrisma = () => new PrismaClient();

const globalForBackofficeDBPrismaClient = globalThis as unknown as {
  backofficeDBPrismaClient: PrismaClient | undefined;
};
export const backofficeDBPrismaClient =
  globalForBackofficeDBPrismaClient.backofficeDBPrismaClient || getPrisma();

if (process.env.NODE_ENV !== 'production')
  globalForBackofficeDBPrismaClient.backofficeDBPrismaClient = backofficeDBPrismaClient;
