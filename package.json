{"name": "cactus-notification-service", "version": "1.0.0", "description": "Cactus Notification Service", "main": "src/index.ts", "scripts": {"dev": "tsx watch src/index.ts", "build": "tsup src/index.ts --format cjs,esm --dts", "start": "node dist/index.js"}, "dependencies": {"@fastify/cors": "^11.1.0", "@fastify/swagger": "^9.5.1", "@prisma/client": "^6.13.0", "fastify": "^5.4.0", "fastify-plugin": "^5.0.1", "ioredis": "^5.7.0", "isomorphic-dompurify": "^2.26.0", "pg": "^8.16.3", "prisma": "^6.13.0", "prom-client": "^15.1.3", "zod": "^4.0.15"}, "devDependencies": {"@types/ms": "^2.1.0", "@types/node": "^24.2.0", "@types/pg": "^8.15.5", "tsx": "^4.20.3", "typescript": "^5.9.2"}}